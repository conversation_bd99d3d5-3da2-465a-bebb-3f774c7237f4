import { useApiCaller } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n';
import { Banner } from '@bika/domains/website/client/layout/banner';
import { IconButton } from '@bika/ui/button-component';
import { Input } from '@bika/ui/forms';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import PlayFilled from '@bika/ui/icons/components/play_filled';
import SearchOutlined from '@bika/ui/icons/components/search_outlined';
import Star2Filled from '@bika/ui/icons/components/star_2_filled';
import { Box, Grid, Stack } from '@bika/ui/layouts';
import { Modal } from '@bika/ui/modal';
import { Skeleton } from '@bika/ui/skeleton';
import { snackbarShow } from '@bika/ui/snackbar';
import { Typography } from '@bika/ui/texts';
import Button from '@mui/joy/Button';
import dayjs from 'dayjs';
import React, { type ChangeEvent, useState } from 'react';
import { BlankPage } from '../../../unit/client/blank-page';

export const AIReplay = () => {
  const { i, t } = useLocale();
  const { trpcQuery } = useApiCaller();
  const utils = trpcQuery.useUtils();
  const [inputValue, setInputValue] = useState('');
  const [searchKey, setSearchKey] = useState('');

  const { data: wizards, isLoading } = trpcQuery.ai.publishedChats.useQuery({
    pageNo: 1,
    pageSize: 10,
    keyword: searchKey,
  });

  const deleteWizard = trpcQuery.ai.deleteWizard.useMutation();

  const onDeleteWizard = (wizardId: string) => {
    deleteWizard.mutate(
      { wizardId },
      {
        onSuccess: () => {
          utils.ai.chatPage.invalidate();
          snackbarShow({
            content: t.delete.delete_success,
            color: 'success',
          });
        },
      },
    );
  };

  if (isLoading) {
    return (
      <Box
        sx={{
          mx: 2,
        }}
      >
        <Skeleton pos="TEMPLATES_CENTER" />
      </Box>
    );
  }

  if (!wizards?.data?.length) {
    return <BlankPage text={'公开的聊天记录为空'} />;
  }

  return (
    <>
      <Banner
        title={'Replays'}
        description={
          'Bika.ai, Business AI Agents platform that combines no-code, billion-row spreadsheet-database, form, wiki notes, automation workflow for your marketing, sales, and projects'
        }
      >
        <Stack mt={2} direction="row" alignItems="center">
          <Input
            placeholder={'Make or search Al Automation apps by your words.'}
            value={inputValue}
            onChange={(e: ChangeEvent<HTMLInputElement>) => {
              setInputValue(e.target.value);
            }}
            startDecorator={<SearchOutlined color="var(--text-secondary)" />}
            sx={{ flex: '1', height: 40, border: 'none' }}
          />
          <Button
            onClick={() => {
              setSearchKey(inputValue.trim());
            }}
            sx={{
              ml: 2,
            }}
            size="lg"
          >
            <Stack mr={1}>
              <Star2Filled />
            </Stack>
            {'Search'}
          </Button>
        </Stack>
      </Banner>
      <Grid
        container
        rowSpacing={2}
        columnSpacing={{ xs: 1, sm: 2, md: 3 }}
        sx={{ width: '100%', padding: 4 }}
      >
        {wizards?.data.map((item) => (
          <Grid xs={3} key={item.id}>
            <Box
              sx={{
                border: '1px solid var(--border-default)',
                borderRadius: '8px',
                '&:hover .delete-button': {
                  display: 'flex',
                },
              }}
            >
              <Box
                height={135}
                display="flex"
                justifyContent="center"
                alignItems="center"
                padding={1}
                sx={{
                  backgroundColor: 'var(--bg-page)',
                  borderTopLeftRadius: '8px',
                  borderTopRightRadius: '8px',
                  position: 'relative',
                }}
              >
                <IconButton
                  className="delete-button"
                  sx={{
                    display: 'none',
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    backgroundColor: 'var(--bg-surface)',
                    borderRadius: '50%',
                    padding: 0,
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    Modal.show({
                      type: 'error',
                      title: t.global.copilot.delete_history,
                      content: t.global.copilot.delete_history_confirm,
                      okText: t.delete.delete,
                      cancelText: t.cancel,
                      onOk: () => onDeleteWizard(item.id),
                    });
                  }}
                >
                  <DeleteOutlined color={'var(--text-secondary)'} />
                </IconButton>
                <Button
                  component="a"
                  href={`/share/${item.share?.id || ''}`}
                  target="_blank"
                  startDecorator={<PlayFilled size={16} currentColor />}
                >
                  {t.ai_consultant.replay}
                </Button>
              </Box>
              <Box
                height={112}
                sx={{
                  backgroundColor: 'var(--bg-surface)',
                  borderBottomLeftRadius: '8px',
                  borderBottomRightRadius: '8px',
                }}
                padding={2}
              >
                <Typography level="b2" textColor="var(--text-primary)" className="truncate">
                  {i(item.title) || t.global.copilot.history_no_title}
                </Typography>
                <Typography
                  level="b4"
                  textColor="var(--text-secondary)"
                  display="-webkit-box"
                  className="line-clamp-2"
                >
                  {i(item.description) || t.global.copilot.history_no_description}
                </Typography>
                <Typography level="b4" textColor="var(--text-secondary)" textAlign="right">
                  {dayjs(item.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                </Typography>
              </Box>
            </Box>
          </Grid>
        ))}
      </Grid>
    </>
  );
};
