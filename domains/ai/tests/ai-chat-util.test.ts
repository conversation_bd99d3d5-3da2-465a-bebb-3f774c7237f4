import { safeValidateTypes } from '@ai-sdk/provider-utils';
import type { AIMessageBO } from '@bika/types/ai/bo';
import { modelMessageSchema } from 'ai';
import fs from 'fs';
import path from 'path';
import { expect, test } from 'vitest';
import { z } from 'zod/v4';
import { enhanceConvertToModelMessages } from '../server/types';

describe('convert ui message to model message test', () => {
  const loadMessages = (id: string): AIMessageBO[] => {
    const file = fs.readFileSync(path.join(__dirname, 'file/message.json'), 'utf8');
    return JSON.parse(file)[id].messages;
  };

  const schema = z.array(modelMessageSchema);

  test('tool output schema error--providerExecuted is null, but should be undefined or boolean', async () => {
    const messages = loadMessages('cha9m9Is9v6uD2OSM8gSUFHS');

    const modelMessages = enhanceConvertToModelMessages(messages);

    const validationResult = await safeValidateTypes({
      value: [modelMessages[7]],
      schema,
    });

    expect(validationResult.success).toBe(true);
  });
});
