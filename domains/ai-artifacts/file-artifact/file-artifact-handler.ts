import { AISO } from '@bika/domains/ai/server/ai-so';
import { downloadUrlToTmpFile, getMimeTypeFromUrl } from '@bika/domains/shared/server/utils/files';
import type { IAIModelSelectBO, PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import type { ArtifactVO, FileArtifactData } from '@bika/types/ai/vo';
import type { FilePart, ImagePart, ModelMessage, TextPart } from 'ai';
import assert from 'assert';
import fs from 'fs';
import type { IStreamResult } from '../../ai/server/types';
import type { UserSO } from '../../user/server/user-so';
import type { AIArtifactSO } from '../ai-artifact-so';
import type { IArtifactHandler } from '../interface';

export class FileArtifactHandler implements IArtifactHandler {
  private _model: PresetLanguageAIModelDef = 'openai/gpt-4.1';

  private _artifactSO: AIArtifactSO;

  public constructor(artifactSO: AIArtifactSO) {
    this._artifactSO = artifactSO;
  }

  async streamResult(_user: UserSO): Promise<IStreamResult | undefined> {
    const data = this._artifactSO.data as FileArtifactData;
    const { filePath, fileType } = data;

    const getFileContent = async (): Promise<TextPart | ImagePart | FilePart> => {
      if (fileType === 'image') {
        return { type: 'image', image: new URL(filePath) };
      }
      const tmpFilePath = await downloadUrlToTmpFile(filePath);
      console.log('🚀 ~ tmpFilePath:', tmpFilePath);
      const fileBuffer = fs.readFileSync(tmpFilePath);

      const mediaType = await getMimeTypeFromUrl(filePath);
      return { type: 'file', data: fileBuffer, mediaType };
    };

    const { prompt, system } = this._artifactSO.prompt;
    assert(prompt, 'FileArtifactHandler:prompt is required');
    assert(system, 'FileArtifactHandler:system is required');

    const fileContent = await getFileContent();

    const messages: ModelMessage[] = [
      {
        role: 'user',
        content: [{ text: prompt, type: 'text' }, fileContent],
      },
    ];
    const model: IAIModelSelectBO = { kind: 'preset', model: this._model };

    const result = await AISO.fileToTextStream({ messages, system }, this._model);

    return {
      type: 'text',
      textResult: result,
      model,
    };
  }

  async getValue(_user: UserSO, streamResult: IStreamResult | undefined): Promise<ArtifactVO> {
    assert(streamResult, 'streamResult should not be undefined');
    assert(streamResult.type === 'text', 'streamResult should be text');

    // Get the filePath from initData (the original file URL)
    const data = this._artifactSO.data as FileArtifactData;
    const { filePath, fileType } = data;

    // Get the AI-generated content interpretation
    const content = await streamResult.textResult.text;

    const vo: ArtifactVO = {
      id: this._artifactSO.id,
      type: 'file',

      data: {
        filePath,
        fileType,
        content,
      },
      usage: await AISO.parseAICreditCost(this._model, await streamResult.textResult.usage),
    };

    return vo;
  }
}
