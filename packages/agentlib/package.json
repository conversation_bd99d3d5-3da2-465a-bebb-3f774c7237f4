{"name": "agentlib", "version": "2.0.0-beta.22", "private": false, "type": "module", "main": "index.ts", "types": "index.ts", "exports": {"*": "./*.ts"}, "scripts": {"test": "vitest run", "lint": "biome format . --write && biome check ."}, "dependencies": {"dockerode": "4.0.7", "redis": "^5.8.2", "tar-stream": "^3.1.7"}, "devDependencies": {"@types/dockerode": "^3.3.43", "@types/tar-stream": "^3.1.4", "typescript": "^5.8.3"}}