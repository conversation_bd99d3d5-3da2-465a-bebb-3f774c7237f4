{"name": "sharelib", "private": true, "version": "2.0.0-beta.22", "description": "Shared and basic utils library for Vika, Bika.ai, and ToolSDK.ai. No index.ts main entry, for tree shaking.", "exports": {"./*": "./*.ts", "./search": "./search/index.ts", "./search/*": "./search/*.ts"}, "scripts": {"test": "vitest"}, "dependencies": {"@elastic/elasticsearch": "^8.13.0", "@node-rs/argon2": "2.0.2", "axios": "^1.10.0", "basenext": "workspace:^", "connection-string": "^4.4.0", "dayjs": "1.11.10", "lodash": "^4.17.21", "mime-types": "^2.1.35", "minio": "7.1.3", "mongoose": "^8.15.1", "negotiator": "^0.6.3", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"vitest": "^3.2.4"}, "keywords": [], "author": "", "license": "ISC"}