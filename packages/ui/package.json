{"name": "@bika/ui", "description": "UI components share across the client components and server components BOTH! Remember to keep it simple, clean, stateless as much as possible", "author": "", "dependencies": {"@anatine/zod-mock": "^3", "@bika/api-caller": "workspace:*", "@bika/contents": "workspace:*", "@bika/types": "workspace:*", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-python": "^6.1.7", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.4", "@dagrejs/dagre": "^1.1.5", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^16.5.0", "@microlink/react-json-view": "^1.23.0", "@monaco-editor/react": "^4.6.0", "@mui/joy": "^5.0.0-beta.52", "@mui/material": "5.18.0", "@tiptap/core": "^2.7.0", "@tiptap/extension-code-block": "^2.8.0", "@tiptap/extension-code-block-lowlight": "^2.8.0", "@tiptap/extension-collaboration": "^2.8.0", "@tiptap/extension-collaboration-cursor": "^2.8.0", "@tiptap/extension-color": "^2.8.0", "@tiptap/extension-document": "^2.8.0", "@tiptap/extension-highlight": "^2.8.0", "@tiptap/extension-image": "^2.8.0", "@tiptap/extension-link": "^2.4.0", "@tiptap/extension-paragraph": "^2.8.0", "@tiptap/extension-placeholder": "^2.8.0", "@tiptap/extension-table": "^2.9.1", "@tiptap/extension-table-cell": "^2.9.1", "@tiptap/extension-table-header": "^2.9.1", "@tiptap/extension-table-row": "^2.9.1", "@tiptap/extension-task-item": "^2.8.0", "@tiptap/extension-task-list": "^2.8.0", "@tiptap/extension-text": "^2.8.0", "@tiptap/extension-text-align": "^2.8.0", "@tiptap/extension-text-style": "^2.8.0", "@tiptap/extension-underline": "^2.8.0", "@tiptap/extension-youtube": "^3.2.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@toolsdk.ai/sdk-ts": "workspace:*", "@types/file-saver": "^2.0.7", "ahooks": "^3.7.10", "basenext": "workspace:*", "classnames": "^2.5.1", "codemirror": "^6.0.1", "file-saver": "^2.0.5", "is-hotkey": "^0.2.0", "lowlight": "^3.1.0", "marked": "^13.0.2", "mermaid": "^11.6.0", "motion": "^12.6.3", "naming-style": "^1.0.1", "pdfjs-dist": "^4.4.168", "react": "18.3.1", "react-big-calendar": "^1.11.0", "react-custom-scrollbars": "^4.2.1", "react-dom": "18.3.1", "react-grid-layout": "^1.5.0", "react-highlight-words": "^0.20.0", "react-markdown": "^9.0.1", "react-mentions": "^4.4.10", "react-multiline-clamp": "^2.0.0", "react-number-format": "^5.4.0", "react-pdf": "^9.1.1", "react-router-dom": "^6.26.0", "react-type-animation": "^3.2.0", "redoc": "^2.1.5", "rehype-highlight": "^7.0.0", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "sharelib": "workspace:*", "turndown": "^7.2.0", "y-indexeddb": "^9.0.12", "y-prosemirror": "^1.2.12", "y-protocols": "^1.0.6", "yjs": "^13.6.19"}, "exports": {"./*": "./src/*.tsx", "./affix": "./src/affix/index.tsx", "./preview-attachment/*": "./src/preview-attachment/*.tsx", "./preview-attachment/interface": "./src/preview-attachment/interface.ts", "./preview-attachment/const": "./src/preview-attachment/const.ts", "./preview-attachment/hooks/*": "./src/preview-attachment/hooks/*.ts", "./utils": "./src/utils.ts", "./toolsdk/*": "./src/toolsdk/*/index.tsx", "./components/*": "./src/components/*.tsx", "./components/*/types": "./src/components/*/types.ts", "./framework": "./src/framework/index.tsx", "./framework/*": "./src/framework/*.tsx", "./icons/*": "./src/icons/*.tsx", "./editor/flow-editor": "./src/editor/flow-editor/index.ts", "./icons": "./src/icons/index.ts", "./ai/writer": "./src/ai/writer/index.ts", "./logo": "./src/logo/index.tsx", "./file": "./src/file/index.ts", "./voice-input": "./src/voice-input/index.tsx", "./ai/type-form/*": "./src/ai/type-form/*.tsx", "./highlighter": "./src/highlighter.tsx", "./backdrop": "./src/backdrop.tsx", "./daterange": "./src/daterange/index.tsx", "./search-input": "./src/search-input-component.tsx", "./search-select": "./src/search-select-component.tsx", "./accordion": "./src/accordion-component.tsx", "./chip": "./src/chip-component.ts", "./markdown": "./src/markdown-component.tsx", "./tips": "./src/tips-component.tsx", "./text-area": "./src/text-area-component.tsx", "./upload-button": "./src/upload-button-component.tsx", "./tooltip": "./src/tooltip-components.tsx", "./user": "./src/user/index.ts", "./avatar-group": "./src/components/avatar/avatar-group.tsx", "./button": "./src/button-component.tsx", "./colors": "./src/colors/index.ts", "./colors/light": "./src/colors/light.ts", "./colors/dark": "./src/colors/dark.ts", "./colors/base": "./src/colors/base/index.ts", "./tag": "./src/tag/index.ts", "./constant": "./src/constant/index.ts", "./divider": "./src/divider.tsx", "./forms": "./src/form-components.tsx", "./form/*": "./src/form/*.tsx", "./accordion-group": "./src/accordion-group/index.tsx", "./auto-form": "./src/form/index.ts", "./icon-button": "./src/icon-button.tsx", "./text-edit": "./src/text-edit-component.tsx", "./textarea-edit": "./src/textarea-edit-component.tsx", "./layouts": "./src/layout-components.tsx", "./web-layout": "./src/layout/index.ts", "./template": "./src/template/index.ts", "./template-card": "./src/template-card/index.tsx", "./modal": "./src/modal-component.tsx", "./sidebar": "./src/sidebar/index.ts", "./wizards": "./src/wizards/index.ts", "./styled": "./src/styled.tsx", "./snackbar": "./src/snackbar/snackbar-component.tsx", "./skeleton": "./src/skeleton.tsx", "./toggle-button-group": "./src/toggle-button-group.tsx", "./text": "./src/text/index.tsx", "./links": "./src/links.tsx", "./coming-soon": "./src/coming-soon-component.tsx", "./tables": "./src/table-components.tsx", "./tabs": "./src/tabs-components.tsx", "./filter": "./src/database/filter/index.tsx", "./texts": "./src/text-components.tsx", "./badge": "./src/badge.tsx", "./dropdown": "./src/dropdown.tsx", "./list": "./src/list.tsx", "./image-crop-upload": "./src/components/image-crop-upload/index.tsx", "./menu": "./src/menu.tsx", "./progress": "./src/progress.tsx", "./breadcrumbs": "./src/breadcrumbs.tsx", "./loading": "./src/loading/index.tsx", "./popover": "./src/popover.tsx", "./stack-navigator": "./src/stack-navigator/index.tsx", "./stack-header-bar": "./src/stack-header-bar/index.tsx", "./loading-mask": "./src/loading-mask.tsx", "./mention": "./src/mention.tsx", "./multi-select": "./src/multi-select.tsx", "./single-select": "./src/single-select.tsx", "./drawer": "./src/drawer.tsx", "./drag-drop-list": "./src/drag-drop-list.tsx", "./json-view": "./src/json-view.tsx", "./lib_colors.css": "./src/lib_colors.css", "./theme.css": "./src/theme.css", "./custom-vars.css": "./src/custom-vars.css", "./empty": "./src/empty/empty.tsx", "./theme-icon": "./src/theme-icon.tsx", "./pagination": "./src/pagination.tsx", "./code-editor": "./src/code-editor.tsx", "./confirm": "./src/confirm.tsx", "./bmenu": "./src/menu/bmenu.tsx", "./right-menu": "./src/right-menu.tsx", "./scrollbar": "./src/scrollbar.tsx", "./x-tree": "./src/x-tree.tsx", "./type-filter": "./src/type-filter/type-filter.tsx", "./website/app-store-icon-path": "./src/website/app-store-icon-path.ts", "./shared/types-form/variables-string-input/utils": "./src/shared/types-form/variables-string-input/utils.ts", "./space-modal-config": "./src/space-modal-config.tsx", "./modal-as-page": "./src/modal-as-page/index.tsx"}, "keywords": [], "license": "ISC", "main": "src/index.ts", "peerDependencies": {"@bika/ui": "workspace:*", "dayjs": "1.11.10", "react-intersection-observer": "^9.10.3"}, "private": true, "type": "module", "devDependencies": {"@floating-ui/dom": "1.6.12", "@floating-ui/react": "0.26.28", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/is-hotkey": "^0.1.10", "@types/react": "^18.2.56", "@types/react-big-calendar": "^1.8.9", "@types/react-custom-scrollbars": "^4.0.13", "@types/react-grid-layout": "^1.3.5", "@types/react-highlight-words": "*", "@types/react-mentions": "^4.1.13", "@types/turndown": "^5.0.4", "@vitejs/plugin-react-swc": "^3.11.0", "react-intersection-observer": "^9.10.3", "shiki": "^3.4.0"}, "version": "2.0.0-beta.22"}