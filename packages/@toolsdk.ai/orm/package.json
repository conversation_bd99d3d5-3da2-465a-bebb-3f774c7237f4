{"name": "@toolsdk.ai/orm", "version": "2.0.0-beta.22", "private": true, "description": "", "main": "index.ts", "exports": {".": "./index.ts", "./*": "./*.ts", "./types": "./types/index.ts"}, "scripts": {"lint": "biome format . --write && biome check .", "db:migrate": "dotenv -e ../../../apps/toolsdk.ai/.env.local  -- prisma migrate dev", "db:deploy": "dotenv -e ../../../apps/toolsdk.ai/.env.local  -- prisma migrate deploy && prisma db seed", "db:gen": "prisma generate", "postinstall": "prisma generate --generator client", "db:seed": "dotenv -e ../../../apps/toolsdk.ai/.env.local  -- tsx prisma/seed/seed.ts", "db:reset": "dotenv -e ../../../apps/toolsdk.ai/.env.local -- prisma migrate reset", "db:reset:force": "dotenv -e ../../../apps/toolsdk.ai/.env.local -- prisma migrate reset --force"}, "prisma": {"seed": "dotenv -e ../../../apps/toolsdk.ai/.env.local -- tsx prisma/seed/seed.ts"}, "dependencies": {"@bika.ai/bika-zapier": "workspace:*", "@bika/types": "workspace:*", "@modelcontextprotocol/sdk": "^1.12.1", "@modelcontextprotocol/server-github": "^2025.3.19", "@modelcontextprotocol/server-slack": "^2025.1.17", "@prisma/client": "6.0.1", "@toolsdk.ai/mcp-server": "workspace:*", "@toolsdk.ai/plugin-core": "workspace:*", "@toolsdk.ai/sdk-ts": "workspace:*", "ai": "^5", "basenext": "workspace:*", "n8n-nodes-base": "^1.14.1", "sharelib": "workspace:*", "zod": "^3.25.0"}, "devDependencies": {"dotenv-cli": "^7.3.0", "prisma": "6.0.1"}, "keywords": [], "author": "", "license": "ISC"}