{"name": "airagent", "private": true, "version": "2.0.0-beta.22", "type": "module", "scripts": {"dev": "next dev --port 3005", "build": "next build", "start": "next start", "lint": "biome format . --write && biome check .", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "next": "15.3.3", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5.8.3"}}