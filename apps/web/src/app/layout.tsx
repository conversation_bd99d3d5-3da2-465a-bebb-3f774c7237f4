import { getServerDictionary } from '@bika/contents/i18n/server';
import { getDictionary } from '@bika/contents/i18n/translate';
import { LicenseHelper } from '@bika/domains/admin/server/license-helper';
import { AttachmentSO } from '@bika/domains/attachment/server/attachment-so';
// import { AuthController } from '@bika/domains/auth/apis';
import { OutgoingWebhookSO } from '@bika/domains/event/server/event/outgoing-webhook-so';
import { isFromCNHost, isFromCNIP } from '@bika/domains/shared/server/utils/http';
import { SiteSsoSO } from '@bika/domains/system/server/site-sso-so';
// import { UserSO } from '@bika/domains/user/server/user-so';
import { WebsiteLayoutNext } from '@bika/domains/website/client/website-layout-next';
import { LOCALE_HEADER } from '@bika/types/shared';
import type { SystemConfiguration } from '@bika/types/system';
import type { ThemeMode, ThemeStyle } from '@bika/types/website/bo';
import type { IWebsiteLayoutInitialData } from '@bika/types/website/context';
import type { Locale } from 'basenext/i18n';
import type { Metadata } from 'next';
import { cookies, headers } from 'next/headers';
import type React from 'react';
import { getAppEnv } from 'sharelib/app-env';
import { getLocaleByHeaders } from 'sharelib/next-utils/get-locale-from-headers';
import '@bika/domains/shared/client/styles/globals.css';
import '@bika/domains/shared/client/styles/markdown.css';
import 'simplebar-react/dist/simplebar.min.css';
import StorybookLayout from './storybook-dev-page/storybook-layout';

export const dynamic = 'force-dynamic';

/**
 * 最上层的generateMetadata，设置最default的title模板
 *
 * @returns
 */
export async function generateMetadata() {
  const dict = getServerDictionary('en');
  const metadata: Metadata = {
    title: {
      template: `%s | ${dict.slogan.slogan_title}`,
      default: dict.slogan.slogan_title,
    },
    description: dict.slogan.slogan_prd_l,
    keywords: dict.slogan.keywords,
    // Print the package.json version number into the HTML <meta>
    other: {
      version: process.env.VERSION as string,
      'apple-itunes-app': 'app-id=6477366601',
    },
    metadataBase: process.env.APP_HOSTNAME ? new URL(process.env.APP_HOSTNAME) : undefined,
    alternates: {
      canonical: './', // 使用相对路径，Next.js 会自动拼接 metadataBase
    },
    icons: [
      {
        rel: 'icon',
        type: 'image/x-icon',
        url: '/assets/favicon-light.ico',
        media: '(prefers-color-scheme: dark)',
      },
      {
        rel: 'icon',
        type: 'image/x-icon',
        url: '/assets/favicon-dark.ico',
        media: '(prefers-color-scheme: light)',
      },
      {
        /** @see https://developers.google.com/search/docs/appearance/favicon-in-search */
        rel: 'icon',
        // type: 'image/x-icon',
        url: '/bika-favicon.ico',
        // media: '(prefers-color-scheme: dark)',
      },
    ],
  };
  return metadata;
}

/**
 * 输出几个东西
 * 1. 用户的主题或者系统主题（auto）
 * 2. 用户的语言或者系统语言 header 取
 * 3. 用户的时区或者系统时区 header 取
 * 4. 是否是移动端 UA 判断
 */
async function getInitialData(): Promise<IWebsiteLayoutInitialData & { themeMode: ThemeMode }> {
  // 默认主题改为 dark
  const h = await headers();

  const locale: Locale = (h.get(LOCALE_HEADER) || getLocaleByHeaders(h)) as Locale;
  const isHome = h.get('x-bika-home') === '1';

  const siteSsoSO = await SiteSsoSO.getSiteSsoInfo();
  let advancedAI = true;
  if (getAppEnv() !== 'LOCAL') {
    const license = await LicenseHelper.checkLicense();
    if (!license) {
      advancedAI = true;
    } else {
      advancedAI = license.advancedAI === true ? license.advancedAI : false;
    }
  }

  let outgoingWebhookUnit = false;
  if (getAppEnv() === 'SELF-HOSTED' || getAppEnv() === 'LOCAL') {
    outgoingWebhookUnit = await OutgoingWebhookSO.hasOutgoingWebhook('ON_MEMBER_INVITE', {
      scope: 'SITE_ADMIN',
    });
  }

  const systemConfiguration: SystemConfiguration = {
    site: {
      sso: siteSsoSO.model,
      outgoingWebhookUnit,
    },
    advancedAI,
  };

  const storagePublicUrl = process.env.STORAGE_PUBLIC_URL || AttachmentSO.publicEndpoint();
  const hostname = process.env.APP_HOSTNAME || '';

  const cookie = await cookies();
  const cookieThemeMode: ThemeMode = (cookie.get('app-theme-mode')?.value || 'dark') as ThemeMode;
  const cookieThemeStyle: ThemeStyle = (cookie.get('app-theme-style')?.value ||
    'default') as ThemeStyle;

  // 官网强制黑色+默认主题，空间站取 cookie 和 user setting
  // isHome 有CDN 所以必须写死 后面js会动态修改
  const themeMode: ThemeMode = isHome ? 'dark' : cookieThemeMode;
  const themeStyle: ThemeStyle = isHome ? 'default' : cookieThemeStyle;
  return {
    hostname,
    headers: h,
    servers: {
      docServerUrl: process.env.DOC_SERVER!,
      formAppAIBaseUrl: process.env.FORMAPP_AI_BASE_URL!,
      toolSDKAIBaseUrl: process.env.TOOLSDK_AI_BASE_URL,
      kkFilePreviewUrl: process.env.KK_FILE_PREVIEW_URL,
      storagePublicUrl,
    },
    systemConfiguration,
    locale,
    isHome,
    themeMode,
    themeStyle,
    isFromCNHost: await isFromCNHost(),
    isFromCN: await isFromCNIP(),
    appEnv: getAppEnv(),
    version: process.env.VERSION || 'unknown',
    env: {
      FIREBASE_API_KEY: process.env.FIREBASE_API_KEY,
      FIREBASE_AUTH_DOMAIN: process.env.FIREBASE_AUTH_DOMAIN,
      FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
      FIREBASE_STORAGE_BUCKET: process.env.FIREBASE_STORAGE_BUCKET,
      FIREBASE_MESSAGING_SENDER_ID: process.env.FIREBASE_MESSAGING_SENDER_ID,
      FIREBASE_APP_ID: process.env.FIREBASE_APP_ID,
      FIREBASE_PUBLIC_VAPID_KEY: process.env.FIREBASE_PUBLIC_VAPID_KEY,
      // 文件类型头像的地址
      STORAGE_PUBLIC_URL: storagePublicUrl,
      DOC_SERVER: process.env.DOC_SERVER,
      FORMAPP_AI_BASE_URL: process.env.FORMAPP_AI_BASE_URL,
      TOOLSDK_AI_BASE_URL: process.env.TOOLSDK_AI_BASE_URL,
    },
  };
}

interface Props {
  children: React.ReactNode;
}
/**
 * Server Component Root Layout
 *
 * @param props
 * @returns
 */
export default async function RootLayout(props: Props) {
  const { children } = props;

  if (process.env.RSC_ENABLED) {
    const dictionary = await getDictionary('en' as Locale);

    console.log('RSC_ENABLED', process.env.RSC_ENABLED);
    return <StorybookLayout dictionary={dictionary}>{props.children}</StorybookLayout>;
  }

  const [h, data] = await Promise.all([headers(), getInitialData()]);

  const dictionary = await getDictionary(data.locale as Locale);

  // 官网强制黑色 ，空间站默认 dark
  // const theme: ThemeMode = data.isHome ? 'dark' : data.themeMode || 'dark';

  // console.log(`global context app env: ${getAppEnv()}`);
  return (
    <WebsiteLayoutNext
      mode={'RSC'}
      dictionary={dictionary}
      // storagePublicUrl={process.env.STORAGE_PUBLIC_URL || AttachmentSO.publicEndpoint()}
      headers={h}
      initialData={data}
      // themeMode={theme}
      // themeMode={theme === 'system' ? (cookie.get('app-theme-mode')?.value as ThemeMode) || 'dark' : theme}
    >
      {children}
    </WebsiteLayoutNext>
  );
}
