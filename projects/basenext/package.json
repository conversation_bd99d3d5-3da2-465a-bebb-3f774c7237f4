{"name": "basenext", "version": "2.0.0-beta.22", "description": "BaseNext is a TypeScript-only NextJS boilerplate for building AI Agent Apps. Build AI Apps Fast with a variety of templates and components.", "exports": {".": "./src/index.ts", "./*": "./src/*/index.ts"}, "scripts": {}, "dependencies": {"@formatjs/intl-localematcher": "^0.5.2", "nanoid": "^5.0.9", "zod": "^3.25.0"}, "devDependencies": {"@types/react": "^18"}, "peerDependencies": {"react": "18.3.1"}, "keywords": [], "author": "", "license": "ISC"}